import { SceneService } from '@/services/scene'
import { SceneParser } from '@/utils/sceneParser'
import { getUrlParam, decodeModelUrl } from '@/utils/url'
import type { IParsedSceneData } from '@/types/scene'
import type { CameraConfig } from '@/types/project'

/**
 * 场景加载器 - 整合API请求和数据处理
 */
export class SceneLoader {
  /**
   * 从URL参数获取项目ID
   * @returns 项目ID，如果没有则返回null
   */
  static getProjectIdFromUrl(): string | null {
    const projectId = getUrlParam('projectId')
    return projectId || null
  }

  /**
   * 从URL参数获取自定义模型URL
   * @returns 解码后的模型URL，如果没有则返回null
   */
  static getCustomModelUrlFromUrl(): string | null {
    const encodedUrl = getUrlParam('model')
    if (encodedUrl) {
      return decodeModelUrl(encodedUrl)
    }
    return null
  }
  /**
   * 从场景信息中获取自定义模型的radius
   * @returns 解码后的模型radius，如果没有则返回10
   */
  static getModelRadius(sceneData: IParsedSceneData): number {
    if (sceneData.scene.modelRadius) {
      try {
        // 尝试直接解析为数字
        const radius = parseFloat(sceneData.scene.modelRadius)
        if (!isNaN(radius) && radius > 0) {
          return radius
        }

        // 尝试解析为JSON数组并取长度
        const radiusArray = JSON.parse(sceneData.scene.modelRadius) as number[]
        if (radiusArray.length > 0) {
          return Math.max(...radiusArray)
        }
      } catch (error) {
        console.warn(`[SceneLoader] 解析modelRadius失败:`, error)
      }
    }

    // 默认半径
    return 10
  }

  /**
   * 根据模型半径计算相机移动速度
   * @param sceneData 场景数据
   * @returns 相机移动速度 (modelRadius的百分之二)
   */
  static getCalculatedCameraMoveSpeed(sceneData: IParsedSceneData): number {
    const modelRadius = this.getModelRadius(sceneData)
    const moveSpeed = modelRadius * 0.02 // 移动速度为模型半径的百分之二

    // 确保移动速度在合理范围内
    return Math.max(0.01, Math.min(0.02, moveSpeed))
  }

  /**
   * 根据项目ID加载完整场景数据
   * @param projectId 项目ID
   * @returns 完整的场景数据
   */
  static async loadSceneData(projectId: string | number): Promise<IParsedSceneData> {
    try {
      console.log(`[SceneLoader] 开始加载场景数据: ${projectId}`)

      // 1. 获取场景详情
      const response = await SceneService.getSceneDetail({ projectId: String(projectId) })
      const scene = response.data

      console.log(`[SceneLoader] 场景数据加载成功:`, scene.name)

      // 2. 解析模型文件
      const models = SceneParser.parseSceneModels(scene.models)
      console.log(`[SceneLoader] 模型文件解析完成:`, models)

      if (projectId === '2126') {
        models.splatUrl = '/splat/yinghuaquangu.splat';
      } else if (projectId === '2148') {
        models.splatUrl = '/splat/tianhan.splat';
      }

      // 3. 获取相机配置和动画轨道
      let cameraConfig: CameraConfig | undefined
      let animTracks: any[] = []

      // 优先尝试获取settings配置
      if (models.settingsUrl) {
        try {
          const settingsData = await SceneService.getConfigFile(models.settingsUrl)
          cameraConfig = SceneParser.parseSettingsToCamera(settingsData) || undefined
          animTracks = settingsData?.animTracks || []
          console.log(`[SceneLoader] Settings相机配置解析完成:`, cameraConfig)
          console.log(`[SceneLoader] Settings动画轨道解析完成:`, animTracks)
        } catch (error) {
          console.warn(`[SceneLoader] 获取settings配置失败:`, error)
        }
      }

      // 如果没有settings配置，尝试transform配置
      if (!cameraConfig && models.transformUrl) {
        try {
          const transformData = await SceneService.getTransformConfig(models.transformUrl)
          cameraConfig = SceneParser.parseTransformToCamera(transformData) || undefined
          console.log(`[SceneLoader] Transform相机配置解析完成:`, cameraConfig)
        } catch (error) {
          console.warn(`[SceneLoader] 获取transform配置失败:`, error)
        }
      }

      return {
        scene,
        models,
        cameraConfig,
        animTracks,
      }
    } catch (error) {
      console.error(`[SceneLoader] 场景数据加载失败:`, error)
      throw error
    }
  }

  /**
   * 获取有效的模型URL（优先使用URL参数，其次使用API数据）
   * @param sceneData 场景数据
   * @returns 有效的模型URL
   */
  static getEffectiveModelUrl(sceneData: IParsedSceneData): string | null {
    // 1. 优先使用URL参数中的自定义模型
    const customModelUrl = this.getCustomModelUrlFromUrl()
    if (customModelUrl) {
      console.log(`[SceneLoader] 使用URL参数中的自定义模型: ${customModelUrl}`)
      return customModelUrl
    }

    // 2. 使用API返回的splat文件
    const apiModelUrl = SceneParser.getEffectiveModelUrl(sceneData.models)
    if (apiModelUrl) {
      console.log(`[SceneLoader] 使用API返回的模型文件: ${apiModelUrl}`)
      return apiModelUrl
    }

    console.warn(`[SceneLoader] 未找到有效的模型URL`)
    return null
  }

  /**
   * 获取有效的相机配置（优先使用transform.json配置）
   * @param sceneData 场景数据
   * @returns 相机配置
   */
  static getEffectiveCameraConfig(sceneData: IParsedSceneData): CameraConfig {
    const defaultConfig: CameraConfig = {
      position: [10, 5, 20],
      lookAt: [0, 0, 0],
      lookUp: [0, -1, 0],
      fov: 50,
      cameraMoveSpeed: 0.01, // 默认值
    }

    console.log(`[SceneLoader] 场景数据:`, sceneData)

    let cameraConfig = { ...defaultConfig }

    // 计算相机移动速度 (新增)
    cameraConfig.cameraMoveSpeed = this.getCalculatedCameraMoveSpeed(sceneData)
    console.log(
      `[SceneLoader] 根据模型半径(${this.getModelRadius(sceneData)})计算相机移动速度:`,
      cameraConfig.cameraMoveSpeed,
    )

    // 如果有settings配置，直接使用
    if (sceneData.cameraConfig && sceneData.models.settingsUrl) {
      cameraConfig = { ...cameraConfig, ...sceneData.cameraConfig }
      console.log(`[SceneLoader] 应用settings相机配置`)
    } else {
      // 没有settings时，先处理modelCenter字段作为lookAt参数
      if (sceneData.scene.modelCenter) {
        try {
          const center = JSON.parse(sceneData.scene.modelCenter) as [number, number, number]
          cameraConfig.lookAt = center
          console.log(`[SceneLoader] 使用modelCenter作为lookAt:`, center)
        } catch (error) {
          console.warn(`[SceneLoader] 解析modelCenter失败:`, error)
        }
      }

      // 然后应用transform.json配置覆盖
      if (sceneData.cameraConfig) {
        cameraConfig = { ...cameraConfig, ...sceneData.cameraConfig }
        console.log(`[SceneLoader] 应用transform.json相机配置`)
      }
    }

    console.log(`[SceneLoader] 最终相机配置:`, cameraConfig)
    return cameraConfig
  }

  /**
   * 智能加载场景（根据URL参数决定加载方式）
   * @returns 场景数据和模型URL
   */
  static async smartLoadScene(): Promise<{
    sceneData: IParsedSceneData | null
    modelUrl: string | null
    cameraConfig: CameraConfig
  }> {
    // 1. 检查是否有项目ID参数
    const projectId = SceneParser.getEffectiveProjectId(getUrlParam('projectName'))

    console.log(`[SceneLoader] 项目ID: ${projectId}`)

    if (projectId) {
      // 有项目ID，从API加载数据
      try {
        const sceneData = await this.loadSceneData(projectId)
        const modelUrl = this.getEffectiveModelUrl(sceneData)
        const cameraConfig = this.getEffectiveCameraConfig(sceneData)

        console.log(`[SceneLoader] 模型URL: ${modelUrl}`)
        console.log(`[SceneLoader] 相机配置: ${JSON.stringify(cameraConfig)}`)

        return { sceneData, modelUrl, cameraConfig }
      } catch (error) {
        console.error(`[SceneLoader] 智能加载失败，回退到自定义模型模式:`, error)
      }
    }

    // 2. 没有项目ID或加载失败，尝试使用URL中的自定义模型
    const customModelUrl = this.getCustomModelUrlFromUrl()
    if (customModelUrl) {
      console.log(`[SceneLoader] 使用自定义模型模式: ${customModelUrl}`)
      return {
        sceneData: null,
        modelUrl: customModelUrl,
        cameraConfig: {
          position: [10, 5, 20],
          lookAt: [0, 0, 0],
          lookUp: [0, -1, 0],
          fov: 50,
        },
      }
    }

    // 3. 都没有，返回空数据
    console.warn(`[SceneLoader] 未找到项目ID或自定义模型URL`)
    return {
      sceneData: null,
      modelUrl: null,
      cameraConfig: {
        position: [10, 5, 20],
        lookAt: [0, 0, 0],
        lookUp: [0, -1, 0],
        fov: 50,
      },
    }
  }
}
